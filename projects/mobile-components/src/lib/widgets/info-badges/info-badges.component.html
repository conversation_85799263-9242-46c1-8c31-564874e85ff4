<div [class]="containerClasses">
  <!-- Display badges -->
  <div
    *ngFor="let badge of displayedBadges; trackBy: trackByBadge"
    [class]="getBadgeClasses(badge)"
    (click)="onBadgeClick(badge)"
    (mouseenter)="onBadgeHover(badge)"
    [attr.aria-label]="badge.label + (badge.value ? ': ' + badge.value : '')"
    [attr.role]="badge.interactive && allowClick ? 'button' : 'status'"
    [attr.tabindex]="badge.interactive && allowClick && !badge.disabled ? '0' : null"
  >
    <!-- Icon -->
    <svg
      *ngIf="showIcons && badge.icon"
      class="w-4 h-4 mr-1.5"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
      [attr.aria-hidden]="true"
    >
      <!-- Default status icon -->
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      ></path>
    </svg>

    <!-- Badge Label -->
    <span class="font-medium">{{ badge.label }}</span>

    <!-- Badge Value -->
    <span *ngIf="showValues && badge.value" class="ml-1 opacity-75">
      ({{ badge.value }})
    </span>

    <!-- Remove Button -->
    <button
      *ngIf="(allowRemove || badge.removable) && !badge.disabled"
      type="button"
      class="ml-1.5 inline-flex items-center justify-center w-4 h-4 text-current hover:bg-black hover:bg-opacity-10 rounded-full transition-colors"
      (click)="onBadgeRemove(badge, $event)"
      [attr.aria-label]="'Remove ' + badge.label"
    >
      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>

  <!-- Show count of hidden badges -->
  <div
    *ngIf="hiddenCount > 0 && showCount"
    class="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-500 bg-gray-100 rounded-md"
  >
    +{{ hiddenCount }} more
  </div>

  <!-- Empty state -->
  <div
    *ngIf="badges.length === 0"
    class="inline-flex items-center px-3 py-1 text-sm text-gray-500 bg-gray-50 rounded-md"
  >
    No badges to display
  </div>
</div>
