import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface BadgeItem {
  id?: string | number;
  label: string;
  value?: string | number;
  icon?: string;
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
  interactive?: boolean;
  removable?: boolean;
  disabled?: boolean;
}

@Component({
  selector: 'lib-info-badges',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './info-badges.component.html',
  styleUrl: './info-badges.component.css'
})
export class InfoBadgesComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() badges: BadgeItem[] = [
    { id: 1, label: 'Active', color: 'success', interactive: true },
    { id: 2, label: 'Pending', color: 'warning', interactive: true },
    { id: 3, label: 'Completed', color: 'primary', interactive: true, removable: true }
  ];
  @Input() layout: 'horizontal' | 'vertical' | 'grid' = 'horizontal';
  @Input() spacing: 'tight' | 'normal' | 'loose' = 'normal';
  @Input() showCount: boolean = false;
  @Input() maxDisplay: number = 0; // 0 means show all
  @Input() allowRemove: boolean = false;
  @Input() allowClick: boolean = true;
  @Input() showIcons: boolean = true;
  @Input() showValues: boolean = true;

  // Event outputs
  @Output() badgeClick = new EventEmitter<BadgeItem>();
  @Output() badgeRemove = new EventEmitter<BadgeItem>();
  @Output() badgeHover = new EventEmitter<BadgeItem>();

  get displayedBadges(): BadgeItem[] {
    if (this.maxDisplay > 0 && this.badges.length > this.maxDisplay) {
      return this.badges.slice(0, this.maxDisplay);
    }
    return this.badges;
  }

  get hiddenCount(): number {
    if (this.maxDisplay > 0 && this.badges.length > this.maxDisplay) {
      return this.badges.length - this.maxDisplay;
    }
    return 0;
  }

  onBadgeClick(badge: BadgeItem) {
    if (!badge.disabled && this.allowClick && badge.interactive) {
      this.badgeClick.emit(badge);
    }
  }

  onBadgeRemove(badge: BadgeItem, event: Event) {
    event.stopPropagation();
    if (!badge.disabled && (this.allowRemove || badge.removable)) {
      this.badgeRemove.emit(badge);
    }
  }

  onBadgeHover(badge: BadgeItem) {
    if (!badge.disabled) {
      this.badgeHover.emit(badge);
    }
  }

  getBadgeClasses(badge: BadgeItem): string {
    const baseClasses = 'inline-flex items-center font-medium transition-colors';

    const sizeClasses = {
      xs: 'px-2 py-0.5 text-xs',
      sm: 'px-2.5 py-0.5 text-sm',
      md: 'px-3 py-1 text-sm',
      lg: 'px-3 py-1.5 text-base',
      xl: 'px-4 py-2 text-lg'
    };

    const colorClasses = {
      default: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
      primary: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
      secondary: 'bg-purple-100 text-purple-800 hover:bg-purple-200',
      success: 'bg-green-100 text-green-800 hover:bg-green-200',
      warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
      danger: 'bg-red-100 text-red-800 hover:bg-red-200',
      info: 'bg-cyan-100 text-cyan-800 hover:bg-cyan-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const interactiveClasses = badge.interactive && this.allowClick && !badge.disabled
      ? 'cursor-pointer hover:shadow-sm'
      : '';

    const disabledClasses = badge.disabled ? 'opacity-50 cursor-not-allowed' : '';

    const badgeColor = badge.color || this.variant;

    return [
      baseClasses,
      sizeClasses[this.size],
      colorClasses[badgeColor],
      roundedClasses[this.rounded],
      interactiveClasses,
      disabledClasses
    ].filter(Boolean).join(' ');
  }

  get containerClasses(): string {
    const baseClasses = 'flex';

    const layoutClasses = {
      horizontal: 'flex-row flex-wrap',
      vertical: 'flex-col',
      grid: 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
    };

    const spacingClasses = {
      tight: 'gap-1',
      normal: 'gap-2',
      loose: 'gap-3'
    };

    return [
      baseClasses,
      layoutClasses[this.layout],
      spacingClasses[this.spacing],
      this.className
    ].filter(Boolean).join(' ');
  }

  trackByBadge(index: number, badge: BadgeItem): any {
    return badge.id || index;
  }
}
