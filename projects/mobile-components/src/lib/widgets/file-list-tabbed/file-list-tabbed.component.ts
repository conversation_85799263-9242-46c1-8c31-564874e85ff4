import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface FileItem {
  id: string | number;
  name: string;
  type: 'file' | 'folder';
  size?: number;
  lastModified?: Date;
  extension?: string;
  icon?: string;
  thumbnail?: string;
  downloadUrl?: string;
  uploadProgress?: number;
  status?: 'uploading' | 'completed' | 'error' | 'pending';
  permissions?: {
    canDownload?: boolean;
    canDelete?: boolean;
    canRename?: boolean;
    canShare?: boolean;
  };
}

export interface FileTab {
  id: string;
  label: string;
  icon?: string;
  count?: number;
  filter?: (file: FileItem) => boolean;
}

@Component({
  selector: 'lib-file-list-tabbed',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './file-list-tabbed.component.html',
  styleUrl: './file-list-tabbed.component.css'
})
export class FileListTabbedComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() files: FileItem[] = [
    {
      id: 1,
      name: 'project-proposal.pdf',
      type: 'file',
      size: 2048576,
      lastModified: new Date(Date.now() - 2 * 60 * 60 * 1000),
      extension: 'pdf',
      status: 'completed',
      permissions: { canDownload: true, canDelete: true, canRename: true, canShare: true }
    },
    {
      id: 2,
      name: 'design-assets',
      type: 'folder',
      lastModified: new Date(Date.now() - 1 * 60 * 60 * 1000),
      permissions: { canDownload: false, canDelete: true, canRename: true, canShare: true }
    },
    {
      id: 3,
      name: 'screenshot.png',
      type: 'file',
      size: 1024000,
      lastModified: new Date(Date.now() - 30 * 60 * 1000),
      extension: 'png',
      status: 'completed',
      permissions: { canDownload: true, canDelete: true, canRename: false, canShare: true }
    },
    {
      id: 4,
      name: 'upload-in-progress.zip',
      type: 'file',
      size: 5242880,
      uploadProgress: 65,
      status: 'uploading',
      extension: 'zip',
      permissions: { canDownload: false, canDelete: true, canRename: false, canShare: false }
    }
  ];

  @Input() tabs: FileTab[] = [
    { id: 'all', label: 'All Files', icon: 'folder', filter: () => true },
    { id: 'documents', label: 'Documents', icon: 'document', filter: (file) => ['pdf', 'doc', 'docx', 'txt'].includes(file.extension || '') },
    { id: 'images', label: 'Images', icon: 'photo', filter: (file) => ['jpg', 'jpeg', 'png', 'gif', 'svg'].includes(file.extension || '') },
    { id: 'uploads', label: 'Uploads', icon: 'upload', filter: (file) => file.status === 'uploading' || file.status === 'pending' }
  ];

  @Input() activeTabId: string = 'all';
  @Input() showThumbnails: boolean = true;
  @Input() showFileSize: boolean = true;
  @Input() showLastModified: boolean = true;
  @Input() showProgress: boolean = true;
  @Input() allowUpload: boolean = true;
  @Input() allowActions: boolean = true;
  @Input() viewMode: 'list' | 'grid' = 'list';

  // Event outputs
  @Output() tabChange = new EventEmitter<string>();
  @Output() fileSelect = new EventEmitter<FileItem>();
  @Output() fileDownload = new EventEmitter<FileItem>();
  @Output() fileDelete = new EventEmitter<FileItem>();
  @Output() fileRename = new EventEmitter<FileItem>();
  @Output() fileShare = new EventEmitter<FileItem>();
  @Output() filesUpload = new EventEmitter<FileList>();

  get activeTab(): FileTab | undefined {
    return this.tabs.find(tab => tab.id === this.activeTabId);
  }

  get filteredFiles(): FileItem[] {
    const activeTab = this.activeTab;
    if (!activeTab || !activeTab.filter) {
      return this.files;
    }
    return this.files.filter(activeTab.filter);
  }

  onTabChange(tabId: string) {
    this.activeTabId = tabId;
    this.tabChange.emit(tabId);
  }

  onFileSelect(file: FileItem) {
    this.fileSelect.emit(file);
  }

  onFileDownload(file: FileItem) {
    if (file.permissions?.canDownload) {
      this.fileDownload.emit(file);
    }
  }

  onFileDelete(file: FileItem) {
    if (file.permissions?.canDelete) {
      this.fileDelete.emit(file);
    }
  }

  onFileRename(file: FileItem) {
    if (file.permissions?.canRename) {
      this.fileRename.emit(file);
    }
  }

  onFileShare(file: FileItem) {
    if (file.permissions?.canShare) {
      this.fileShare.emit(file);
    }
  }

  onFilesUpload(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      this.filesUpload.emit(input.files);
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatLastModified(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  getFileIcon(file: FileItem): string {
    if (file.type === 'folder') return 'folder';

    const iconMap: { [key: string]: string } = {
      'pdf': 'document-text',
      'doc': 'document-text',
      'docx': 'document-text',
      'txt': 'document-text',
      'jpg': 'photograph',
      'jpeg': 'photograph',
      'png': 'photograph',
      'gif': 'photograph',
      'svg': 'photograph',
      'zip': 'archive',
      'rar': 'archive',
      'mp4': 'film',
      'avi': 'film',
      'mp3': 'music-note',
      'wav': 'music-note'
    };

    return iconMap[file.extension || ''] || 'document';
  }

  get containerClasses(): string {
    const baseClasses = 'w-full';
    return [baseClasses, this.className].filter(Boolean).join(' ');
  }

  get tabClasses(): string {
    const baseClasses = 'border-b border-gray-200';
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    return [baseClasses, sizeClasses[this.size]].filter(Boolean).join(' ');
  }

  getTabButtonClasses(isActive: boolean): string {
    const baseClasses = 'px-4 py-2 font-medium border-b-2 transition-colors';
    const activeClasses = isActive
      ? 'border-blue-500 text-blue-600'
      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300';

    return [baseClasses, activeClasses].filter(Boolean).join(' ');
  }

  getFileItemClasses(): string {
    const baseClasses = 'flex items-center p-3 hover:bg-gray-50 transition-colors border-b border-gray-100';
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [baseClasses, roundedClasses[this.rounded]].filter(Boolean).join(' ');
  }

  trackByFile(index: number, file: FileItem): any {
    return file.id;
  }

  trackByTab(index: number, tab: FileTab): any {
    return tab.id;
  }
}
