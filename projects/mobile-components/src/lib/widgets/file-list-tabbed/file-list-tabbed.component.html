<div [class]="containerClasses">
  <!-- Tabs Navigation -->
  <div [class]="tabClasses">
    <nav class="flex space-x-8" aria-label="File categories">
      <button
        *ngFor="let tab of tabs; trackBy: trackByTab"
        type="button"
        [class]="getTabButtonClasses(tab.id === activeTabId)"
        (click)="onTabChange(tab.id)"
        [attr.aria-selected]="tab.id === activeTabId"
        [attr.aria-controls]="'tab-panel-' + tab.id"
      >
        <!-- Tab Icon -->
        <svg *ngIf="tab.icon" class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <!-- Generic folder icon -->
          <path *ngIf="tab.icon === 'folder'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
          <!-- Document icon -->
          <path *ngIf="tab.icon === 'document'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          <!-- Photo icon -->
          <path *ngIf="tab.icon === 'photo'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          <!-- Upload icon -->
          <path *ngIf="tab.icon === 'upload'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
        </svg>

        {{ tab.label }}

        <!-- Count badge -->
        <span *ngIf="tab.count !== undefined"
              class="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
          {{ tab.count }}
        </span>
      </button>
    </nav>
  </div>

  <!-- Upload Area -->
  <div *ngIf="allowUpload" class="p-4 border-b border-gray-200">
    <label class="flex items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
      <div class="flex flex-col items-center justify-center pt-5 pb-6">
        <svg class="w-8 h-8 mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
        </svg>
        <p class="mb-2 text-sm text-gray-500">
          <span class="font-semibold">Click to upload</span> or drag and drop
        </p>
        <p class="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
      </div>
      <input type="file" class="hidden" multiple (change)="onFilesUpload($event)" />
    </label>
  </div>

  <!-- File List -->
  <div class="bg-white" [attr.id]="'tab-panel-' + activeTabId" role="tabpanel">
    <!-- File Items -->
    <div
      *ngFor="let file of filteredFiles; trackBy: trackByFile"
      [class]="getFileItemClasses()"
      (click)="onFileSelect(file)"
      [attr.aria-label]="file.name + ' - ' + (file.type === 'folder' ? 'Folder' : 'File')"
      role="button"
      tabindex="0"
    >
      <!-- File Icon -->
      <div class="flex-shrink-0 mr-3">
        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <!-- Folder icon -->
          <path *ngIf="file.type === 'folder'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
          <!-- File icon -->
          <path *ngIf="file.type === 'file'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>

      <!-- File Info -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center justify-between">
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
            <div class="flex items-center space-x-2 text-xs text-gray-500">
              <span *ngIf="showFileSize && file.size">{{ formatFileSize(file.size) }}</span>
              <span *ngIf="showLastModified && file.lastModified">{{ formatLastModified(file.lastModified) }}</span>
            </div>
          </div>

          <!-- Status and Progress -->
          <div class="flex items-center space-x-2">
            <!-- Upload Progress -->
            <div *ngIf="showProgress && file.status === 'uploading' && file.uploadProgress !== undefined"
                 class="flex items-center space-x-2">
              <div class="w-16 bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                     [style.width.%]="file.uploadProgress"></div>
              </div>
              <span class="text-xs text-gray-500">{{ file.uploadProgress }}%</span>
            </div>

            <!-- Status Badge -->
            <span *ngIf="file.status && file.status !== 'completed'"
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  [class.bg-yellow-100]="file.status === 'uploading'"
                  [class.text-yellow-800]="file.status === 'uploading'"
                  [class.bg-red-100]="file.status === 'error'"
                  [class.text-red-800]="file.status === 'error'"
                  [class.bg-gray-100]="file.status === 'pending'"
                  [class.text-gray-800]="file.status === 'pending'">
              {{ file.status }}
            </span>
          </div>
        </div>
      </div>

      <!-- Actions Menu -->
      <div *ngIf="allowActions" class="flex-shrink-0 ml-3">
        <div class="relative">
          <button
            type="button"
            class="p-1 text-gray-400 hover:text-gray-600 transition-colors"
            [attr.aria-label]="'Actions for ' + file.name"
          >
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"></path>
            </svg>
          </button>

          <!-- Actions dropdown would be implemented here -->
          <div class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
            <div class="py-1">
              <button *ngIf="file.permissions?.canDownload"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      (click)="onFileDownload(file)">
                Download
              </button>
              <button *ngIf="file.permissions?.canRename"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      (click)="onFileRename(file)">
                Rename
              </button>
              <button *ngIf="file.permissions?.canShare"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      (click)="onFileShare(file)">
                Share
              </button>
              <button *ngIf="file.permissions?.canDelete"
                      class="block w-full text-left px-4 py-2 text-sm text-red-700 hover:bg-red-50"
                      (click)="onFileDelete(file)">
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredFiles.length === 0" class="text-center py-12">
      <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z"></path>
      </svg>
      <h3 class="text-sm font-medium text-gray-900 mb-1">No files found</h3>
      <p class="text-sm text-gray-500">{{ activeTab?.label || 'This category' }} is empty</p>
    </div>
  </div>
</div>
