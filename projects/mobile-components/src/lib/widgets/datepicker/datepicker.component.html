<div class="relative" #calendarContainer>
  <!-- Label -->
  <label *ngIf="label" class="block text-sm font-medium text-gray-700 mb-1">
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </label>

  <!-- Input Field -->
  <div class="relative">
    <input
      type="text"
      [value]="inputValue"
      [placeholder]="config.placeholder || placeholder"
      [disabled]="disabled"
      [readonly]="true"
      [class]="inputClasses"
      (click)="toggleCalendar()"
      [attr.aria-label]="label || 'Date picker'"
      [attr.aria-expanded]="isCalendarOpen"
      [attr.aria-haspopup]="true"
    />

    <!-- Calendar Icon -->
    <div *ngIf="showIcon" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
      <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
        </path>
      </svg>
    </div>
  </div>

  <!-- Error Message -->
  <p *ngIf="errorMessage" class="mt-1 text-sm text-red-600">
    {{ errorMessage }}
  </p>

  <!-- Calendar Popup -->
  <div *ngIf="isCalendarOpen" [class]="calendarClasses">
    <!-- Calendar Header -->
    <div class="flex items-center justify-between mb-4">
      <button
        type="button"
        class="p-1 hover:bg-gray-100 rounded"
        (click)="navigateMonth('prev')"
        aria-label="Previous month"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>

      <h3 class="text-lg font-semibold">{{ monthYearDisplay }}</h3>

      <button
        type="button"
        class="p-1 hover:bg-gray-100 rounded"
        (click)="navigateMonth('next')"
        aria-label="Next month"
      >
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>

    <!-- Week Days Header -->
    <div class="grid grid-cols-7 gap-1 mb-2">
      <div *ngFor="let day of weekDays" class="text-center text-sm font-medium text-gray-500 py-2">
        {{ day }}
      </div>
    </div>

    <!-- Calendar Days -->
    <div class="grid grid-cols-7 gap-1">
      <button
        *ngFor="let date of calendarDays"
        type="button"
        class="relative p-2 text-sm rounded hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
        [class.text-gray-400]="!isDateCurrentMonth(date)"
        [class.bg-blue-500]="isDateSelected(date) && isDateCurrentMonth(date)"
        [class.text-white]="isDateSelected(date) && isDateCurrentMonth(date)"
        [class.bg-blue-100]="isDateToday(date) && !isDateSelected(date)"
        [class.cursor-not-allowed]="isDateDisabled(date)"
        [class.opacity-50]="isDateDisabled(date)"
        [disabled]="isDateDisabled(date)"
        (click)="selectDate(date)"
        [attr.aria-label]="date.toDateString()"
      >
        {{ date.getDate() }}
        <span *ngIf="isDateToday(date) && !isDateSelected(date)"
              class="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full">
        </span>
      </button>
    </div>

    <!-- Calendar Footer -->
    <div class="flex justify-between items-center mt-4 pt-3 border-t border-gray-200">
      <button
        *ngIf="config.showTodayButton"
        type="button"
        class="px-3 py-1 text-sm text-blue-600 hover:bg-blue-50 rounded"
        (click)="selectToday()"
      >
        Today
      </button>

      <button
        *ngIf="config.showClearButton"
        type="button"
        class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-50 rounded"
        (click)="clearSelection()"
      >
        Clear
      </button>
    </div>
  </div>
</div>
