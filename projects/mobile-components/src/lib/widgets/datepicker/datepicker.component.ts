import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef, ViewChild, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface DateRange {
  start: Date | null;
  end: Date | null;
}

export interface DatepickerConfig {
  minDate?: Date;
  maxDate?: Date;
  disabledDates?: Date[];
  locale?: string;
  format?: string;
  placeholder?: string;
  showClearButton?: boolean;
  showTodayButton?: boolean;
}

@Component({
  selector: 'lib-datepicker',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './datepicker.component.html',
  styleUrl: './datepicker.component.css'
})
export class DatepickerComponent implements OnInit, OnDestroy {
  @ViewChild('calendarContainer', { static: false }) calendarContainer!: ElementRef;

  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() value: Date | null = null;
  @Input() rangeMode: boolean = false;
  @Input() rangeValue: DateRange = { start: null, end: null };
  @Input() config: DatepickerConfig = {};
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() required: boolean = false;
  @Input() label: string = '';
  @Input() placeholder: string = 'Select date';
  @Input() errorMessage: string = '';
  @Input() showIcon: boolean = true;

  // Event outputs
  @Output() valueChange = new EventEmitter<Date | null>();
  @Output() rangeValueChange = new EventEmitter<DateRange>();
  @Output() dateSelect = new EventEmitter<Date>();
  @Output() calendarOpen = new EventEmitter<void>();
  @Output() calendarClose = new EventEmitter<void>();

  // Internal state
  isCalendarOpen: boolean = false;
  currentMonth: Date = new Date();
  selectedDate: Date | null = null;
  selectedRange: DateRange = { start: null, end: null };
  calendarDays: Date[] = [];
  inputValue: string = '';

  ngOnInit() {
    this.initializeComponent();
    this.generateCalendar();
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  private initializeComponent() {
    this.selectedDate = this.value;
    this.selectedRange = { ...this.rangeValue };
    this.updateInputValue();

    // Set default config values
    this.config = {
      locale: 'en-US',
      format: 'MM/dd/yyyy',
      placeholder: this.placeholder,
      showClearButton: true,
      showTodayButton: true,
      ...this.config
    };
  }

  private updateInputValue() {
    if (this.rangeMode && this.selectedRange.start) {
      if (this.selectedRange.end) {
        this.inputValue = `${this.formatDate(this.selectedRange.start)} - ${this.formatDate(this.selectedRange.end)}`;
      } else {
        this.inputValue = this.formatDate(this.selectedRange.start);
      }
    } else if (this.selectedDate) {
      this.inputValue = this.formatDate(this.selectedDate);
    } else {
      this.inputValue = '';
    }
  }

  private formatDate(date: Date): string {
    const format = this.config.format || 'MM/dd/yyyy';
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();

    return format
      .replace('MM', month)
      .replace('dd', day)
      .replace('yyyy', year.toString());
  }

  toggleCalendar() {
    if (this.disabled || this.readonly) return;

    this.isCalendarOpen = !this.isCalendarOpen;
    if (this.isCalendarOpen) {
      this.calendarOpen.emit();
      this.generateCalendar();
    } else {
      this.calendarClose.emit();
    }
  }

  closeCalendar() {
    this.isCalendarOpen = false;
    this.calendarClose.emit();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    if (this.calendarContainer && !this.calendarContainer.nativeElement.contains(event.target)) {
      this.closeCalendar();
    }
  }

  generateCalendar() {
    const year = this.currentMonth.getFullYear();
    const month = this.currentMonth.getMonth();

    // Get first day of month and calculate starting date
    const firstDay = new Date(year, month, 1);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    // Generate 42 days (6 weeks)
    this.calendarDays = [];
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      this.calendarDays.push(date);
    }
  }

  selectDate(date: Date) {
    if (this.isDateDisabled(date)) return;

    if (this.rangeMode) {
      this.selectRangeDate(date);
    } else {
      this.selectedDate = date;
      this.value = date;
      this.valueChange.emit(date);
      this.updateInputValue();
      this.closeCalendar();
    }

    this.dateSelect.emit(date);
  }

  private selectRangeDate(date: Date) {
    if (!this.selectedRange.start || (this.selectedRange.start && this.selectedRange.end)) {
      // Start new range
      this.selectedRange = { start: date, end: null };
    } else if (this.selectedRange.start && !this.selectedRange.end) {
      // Complete range
      if (date < this.selectedRange.start) {
        this.selectedRange = { start: date, end: this.selectedRange.start };
      } else {
        this.selectedRange.end = date;
      }
      this.rangeValue = { ...this.selectedRange };
      this.rangeValueChange.emit(this.rangeValue);
      this.updateInputValue();
      this.closeCalendar();
    }
  }

  isDateDisabled(date: Date): boolean {
    if (this.config.minDate && date < this.config.minDate) return true;
    if (this.config.maxDate && date > this.config.maxDate) return true;
    if (this.config.disabledDates?.some(d => this.isSameDate(d, date))) return true;
    return false;
  }

  isDateSelected(date: Date): boolean {
    if (this.rangeMode) {
      return this.isDateInRange(date);
    }
    return this.selectedDate ? this.isSameDate(this.selectedDate, date) : false;
  }

  isDateInRange(date: Date): boolean {
    if (!this.selectedRange.start) return false;
    if (!this.selectedRange.end) return this.isSameDate(this.selectedRange.start, date);
    return date >= this.selectedRange.start && date <= this.selectedRange.end;
  }

  isDateToday(date: Date): boolean {
    return this.isSameDate(date, new Date());
  }

  isDateCurrentMonth(date: Date): boolean {
    return date.getMonth() === this.currentMonth.getMonth() &&
           date.getFullYear() === this.currentMonth.getFullYear();
  }

  private isSameDate(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  navigateMonth(direction: 'prev' | 'next') {
    const newMonth = new Date(this.currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    this.currentMonth = newMonth;
    this.generateCalendar();
  }

  selectToday() {
    const today = new Date();
    this.selectDate(today);
  }

  clearSelection() {
    if (this.rangeMode) {
      this.selectedRange = { start: null, end: null };
      this.rangeValue = { ...this.selectedRange };
      this.rangeValueChange.emit(this.rangeValue);
    } else {
      this.selectedDate = null;
      this.value = null;
      this.valueChange.emit(null);
    }
    this.updateInputValue();
  }

  get monthYearDisplay(): string {
    return this.currentMonth.toLocaleDateString(this.config.locale || 'en-US', {
      month: 'long',
      year: 'numeric'
    });
  }

  get weekDays(): string[] {
    const days = [];
    const date = new Date();
    // Start from Sunday (0)
    for (let i = 0; i < 7; i++) {
      date.setDate(date.getDate() - date.getDay() + i);
      days.push(date.toLocaleDateString(this.config.locale || 'en-US', { weekday: 'short' }));
    }
    return days;
  }

  // Computed CSS classes
  get inputClasses(): string {
    const baseClasses = 'relative w-full border focus:outline-none focus:ring-2 transition-colors';
    const sizeClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-3 py-2 text-base',
      lg: 'px-4 py-2.5 text-lg',
      xl: 'px-4 py-3 text-xl'
    };
    const variantClasses = {
      default: 'border-gray-300 focus:border-blue-500 focus:ring-blue-200',
      primary: 'border-blue-300 focus:border-blue-500 focus:ring-blue-200',
      secondary: 'border-gray-300 focus:border-gray-500 focus:ring-gray-200',
      success: 'border-green-300 focus:border-green-500 focus:ring-green-200',
      warning: 'border-yellow-300 focus:border-yellow-500 focus:ring-yellow-200',
      danger: 'border-red-300 focus:border-red-500 focus:ring-red-200'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';
    const errorClasses = this.errorMessage ? 'border-red-500 focus:border-red-500 focus:ring-red-200' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      errorClasses || variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get calendarClasses(): string {
    return 'absolute z-50 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-4 min-w-80';
  }
}
