<!-- Enhanced Avatar Group Component -->
<div
  [class]="containerClasses()"
  [attr.aria-label]="'Avatar group with ' + avatars.length + ' members'"
  role="group"
>
  <!-- Before content slot -->
  <ng-content select="[slot='before']"></ng-content>

  <!-- Loading state -->
  <div *ngIf="loading" class="avatar-group-loading">
    <div 
      *ngFor="let item of displayAvatars(); trackBy: trackByIndex"
      [class]="avatarClasses()"
    >
      <div class="avatar-skeleton" [attr.aria-label]="'Loading avatar'">
        <div class="skeleton-content"></div>
      </div>
    </div>nd
  </div>

  <!-- Empty state -->
  <div 
    *ngIf="shouldShowEmptyState()" 
    class="avatar-group-empty"
    [attr.aria-label]="emptyText"
  >
    <div class="empty-icon">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="8" r="3" stroke="currentColor" stroke-width="2" fill="none"/>
        <path d="M12 14c-4 0-7 2-7 5v1h14v-1c0-3-3-5-7-5z" stroke="currentColor" stroke-width="2" fill="none"/>
      </svg>
    </div>
    <span class="empty-text">{{ emptyText }}</span>
  </div>

  <!-- Avatar list -->
  <div 
    *ngIf="!loading && !shouldShowEmptyState()" 
    class="avatar-group-list"
  >
    <!-- Individual avatars -->
    <div
      *ngFor="let avatar of displayAvatars(); trackBy: trackByAvatarId; let i = index"
      [class]="avatarClasses()"
      [style.z-index]="displayAvatars().length - i"
      [attr.aria-label]="avatar.tooltip || avatar.name || 'Avatar ' + (i + 1)"
      [attr.title]="showTooltips && (avatar.tooltip || avatar.name) ? (avatar.tooltip || avatar.name) : null"
      (click)="onAvatarClick(avatar)"
      (mouseenter)="onAvatarHover(avatar)"
      [attr.tabindex]="(avatar.clickable !== false && clickableAvatars) ? '0' : null"
      [attr.role]="(avatar.clickable !== false && clickableAvatars) ? 'button' : 'img'"
      (keydown.enter)="onAvatarClick(avatar)"
      (keydown.space)="onAvatarClick(avatar)"
    >
      <base-avatar
        [src]="avatar.src"
        [srcDark]="avatar.srcDark"
        [text]="getInitials(avatar)"
        [size]="getAvatarSize()"
        [rounded]="rounded"
        [color]="getAvatarColor(avatar)"
        [ring]="avatar.ring"
        [dot]="avatar.dot"
        [badgeSrc]="avatar.badge?.src"
        [class]="'avatar-item'"
      ></base-avatar>
    </div>

    <!-- Count indicator for hidden avatars -->
    <div
      *ngIf="shouldShowCount()"
      [class]="countClasses()"
      [attr.aria-label]="'Show ' + hiddenCount() + ' more avatars'"
      [attr.title]="showTooltips ? 'Show ' + hiddenCount() + ' more' : null"
      (click)="onCountClick()"
      [attr.tabindex]="clickableAvatars ? '0' : null"
      [attr.role]="clickableAvatars ? 'button' : 'text'"
      (keydown.enter)="onCountClick()"
      (keydown.space)="onCountClick()"
    >
      <div class="count-content">
        <span class="count-text">+{{ hiddenCount() }}</span>
      </div>
    </div>
  </div>

  <!-- After content slot -->
  <ng-content select="[slot='after']"></ng-content>

  <!-- Legacy support wrapper (hidden by default, maintained for backward compatibility) -->
  <div 
    class="nui-avatar-group legacy-wrapper" 
    [ngClass]="[size && sizes[size], classes?.wrapper]"
    style="display: none;"
  >
    <div
      *ngFor="let avatar of avatarDisplay()"
      class="nui-avatar-outer"
      [ngClass]="classes?.outer"
    >
      <base-avatar
        [ngClass]="'bg-primary-500/20 text-primary-500 !scale-90'"
        [src]="avatar?.src"
        [size]="size"
        rounded="full"
        tabindex="0"
        (click)="avatarSelected.emit(isString(avatar) ? null : avatar.id)"
      ></base-avatar>
    </div>
    <div
      *ngIf="limit !== undefined && avatars.length > limit"
      class="nui-avatar-count"
      [ngClass]="classes?.count"
    >
      <div class="nui-avatar-count-inner">
        <span class="nui-avatar-count-text">
          +{{ avatars.length - limit + 1 }}
        </span>
      </div>
    </div>
  </div>
</div>
