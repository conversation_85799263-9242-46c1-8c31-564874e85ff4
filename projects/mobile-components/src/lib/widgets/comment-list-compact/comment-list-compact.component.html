<div [class]="containerClasses">
  <!-- Comments List -->
  <div
    *ngFor="let comment of comments; trackBy: trackByComment"
    [class]="getCommentClasses()"
    [attr.aria-label]="'Comment by ' + comment.author.name"
  >
    <!-- Pinned indicator -->
    <div *ngIf="comment.isPinned" class="flex items-center mb-2 text-sm text-blue-600">
      <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
        <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"></path>
        <path fill-rule="evenodd" d="M3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
        <path d="M8 11v6h4v-6H8z"></path>
      </svg>
      Pinned
    </div>

    <!-- Comment Header -->
    <div class="flex items-start space-x-3">
      <!-- Avatar -->
      <div *ngIf="showAvatars" class="flex-shrink-0">
        <div
          *ngIf="!comment.author.avatar"
          class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-sm font-medium text-gray-700"
        >
          {{ getAvatarInitials(comment.author.name) }}
        </div>
        <img
          *ngIf="comment.author.avatar"
          [src]="comment.author.avatar"
          [alt]="comment.author.name"
          class="w-8 h-8 rounded-full object-cover"
        />
      </div>

      <!-- Comment Content -->
      <div class="flex-1 min-w-0">
        <!-- Author and metadata -->
        <div class="flex items-center space-x-2 mb-1">
          <span class="font-medium text-gray-900">{{ comment.author.name }}</span>
          <span *ngIf="showRoles && comment.author.role" class="text-sm text-gray-500">
            {{ comment.author.role }}
          </span>
          <span *ngIf="showTimestamps" class="text-sm text-gray-500">
            {{ formatTimestamp(comment.timestamp) }}
          </span>
          <span *ngIf="comment.isEdited" class="text-xs text-gray-400">(edited)</span>
        </div>

        <!-- Comment text -->
        <div class="text-gray-700 mb-2" [class.line-through]="comment.isDeleted">
          <span *ngIf="!comment.isDeleted">{{ comment.content }}</span>
          <span *ngIf="comment.isDeleted" class="italic text-gray-400">This comment was deleted</span>
        </div>

        <!-- Actions and voting -->
        <div class="flex items-center space-x-4 text-sm">
          <!-- Voting -->
          <div *ngIf="showVoting && comment.votes && !comment.isDeleted" class="flex items-center space-x-2">
            <button
              type="button"
              class="flex items-center space-x-1 text-gray-500 hover:text-green-600 transition-colors"
              [class.text-green-600]="comment.votes.userVote === 'up'"
              (click)="onVote(comment, 'up')"
              [attr.aria-label]="'Upvote comment by ' + comment.author.name"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
              </svg>
              <span>{{ comment.votes.upvotes }}</span>
            </button>

            <button
              type="button"
              class="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors"
              [class.text-red-600]="comment.votes.userVote === 'down'"
              (click)="onVote(comment, 'down')"
              [attr.aria-label]="'Downvote comment by ' + comment.author.name"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
              <span>{{ comment.votes.downvotes }}</span>
            </button>
          </div>

          <!-- Like -->
          <button
            *ngIf="comment.metadata?.likes !== undefined && !comment.isDeleted"
            type="button"
            class="flex items-center space-x-1 text-gray-500 hover:text-red-500 transition-colors"
            [class.text-red-500]="comment.metadata?.isLiked"
            (click)="onLike(comment)"
            [attr.aria-label]="'Like comment by ' + comment.author.name"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
            </svg>
            <span>{{ comment.metadata?.likes }}</span>
          </button>

          <!-- Reply -->
          <button
            *ngIf="allowActions && comment.metadata?.canReply && !comment.isDeleted"
            type="button"
            class="text-gray-500 hover:text-blue-600 transition-colors"
            (click)="onReply(comment)"
          >
            Reply
          </button>

          <!-- Edit -->
          <button
            *ngIf="allowActions && comment.metadata?.canEdit && !comment.isDeleted"
            type="button"
            class="text-gray-500 hover:text-blue-600 transition-colors"
            (click)="onEdit(comment)"
          >
            Edit
          </button>

          <!-- Delete -->
          <button
            *ngIf="allowActions && comment.metadata?.canDelete && !comment.isDeleted"
            type="button"
            class="text-gray-500 hover:text-red-600 transition-colors"
            (click)="onDelete(comment)"
          >
            Delete
          </button>
        </div>

        <!-- Replies -->
        <div *ngIf="showReplies && comment.replies && comment.replies.length > 0" class="mt-3 ml-4 border-l-2 border-gray-200 pl-4">
          <div class="text-sm text-gray-600 mb-2">
            {{ comment.replies.length }} {{ comment.replies.length === 1 ? 'reply' : 'replies' }}
          </div>
          <!-- Recursive replies would go here in a full implementation -->
        </div>
      </div>
    </div>
  </div>

  <!-- Load more button -->
  <div *ngIf="comments.length >= pageSize" class="text-center pt-4">
    <button
      type="button"
      class="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors"
      (click)="onLoadMore()"
    >
      Load more comments
    </button>
  </div>

  <!-- Empty state -->
  <div *ngIf="comments.length === 0" class="text-center py-8 text-gray-500">
    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
    </svg>
    <p>No comments yet</p>
    <p class="text-sm">Be the first to share your thoughts!</p>
  </div>
</div>
