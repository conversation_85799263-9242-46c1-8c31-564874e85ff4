import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface CommentItem {
  id: string | number;
  author: {
    name: string;
    avatar?: string;
    role?: string;
  };
  content: string;
  timestamp: Date | string;
  votes?: {
    upvotes: number;
    downvotes: number;
    userVote?: 'up' | 'down' | null;
  };
  replies?: CommentItem[];
  isEdited?: boolean;
  isPinned?: boolean;
  isDeleted?: boolean;
  metadata?: {
    likes?: number;
    isLiked?: boolean;
    canEdit?: boolean;
    canDelete?: boolean;
    canReply?: boolean;
  };
}

@Component({
  selector: 'lib-comment-list-compact',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './comment-list-compact.component.html',
  styleUrl: './comment-list-compact.component.css'
})
export class CommentListCompactComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() comments: CommentItem[] = [
    {
      id: 1,
      author: { name: 'John Doe', avatar: '', role: 'Developer' },
      content: 'This is a great feature! Looking forward to using it in production.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      votes: { upvotes: 5, downvotes: 0, userVote: null },
      metadata: { likes: 3, isLiked: false, canEdit: false, canDelete: false, canReply: true }
    },
    {
      id: 2,
      author: { name: 'Jane Smith', avatar: '', role: 'Designer' },
      content: 'The UI looks clean and intuitive. Nice work on the accessibility features.',
      timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
      votes: { upvotes: 3, downvotes: 1, userVote: 'up' },
      metadata: { likes: 2, isLiked: true, canEdit: true, canDelete: true, canReply: true },
      isPinned: true
    },
    {
      id: 3,
      author: { name: 'Mike Johnson', avatar: '', role: 'Product Manager' },
      content: 'Could we add pagination support for large comment threads?',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      votes: { upvotes: 2, downvotes: 0, userVote: null },
      metadata: { likes: 1, isLiked: false, canEdit: false, canDelete: false, canReply: true }
    }
  ];

  @Input() showVoting: boolean = true;
  @Input() showReplies: boolean = true;
  @Input() showTimestamps: boolean = true;
  @Input() showAvatars: boolean = true;
  @Input() showRoles: boolean = true;
  @Input() maxDepth: number = 3;
  @Input() pageSize: number = 10;
  @Input() allowActions: boolean = true;
  @Input() compactMode: boolean = true;

  // Event outputs
  @Output() commentVote = new EventEmitter<{comment: CommentItem, vote: 'up' | 'down'}>();
  @Output() commentLike = new EventEmitter<CommentItem>();
  @Output() commentReply = new EventEmitter<CommentItem>();
  @Output() commentEdit = new EventEmitter<CommentItem>();
  @Output() commentDelete = new EventEmitter<CommentItem>();
  @Output() loadMore = new EventEmitter<void>();

  onVote(comment: CommentItem, vote: 'up' | 'down') {
    this.commentVote.emit({ comment, vote });
  }

  onLike(comment: CommentItem) {
    this.commentLike.emit(comment);
  }

  onReply(comment: CommentItem) {
    this.commentReply.emit(comment);
  }

  onEdit(comment: CommentItem) {
    this.commentEdit.emit(comment);
  }

  onDelete(comment: CommentItem) {
    this.commentDelete.emit(comment);
  }

  onLoadMore() {
    this.loadMore.emit();
  }

  formatTimestamp(timestamp: Date | string): string {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  getAvatarInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }

  get containerClasses(): string {
    const baseClasses = 'space-y-4';
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    return [
      baseClasses,
      sizeClasses[this.size],
      this.className
    ].filter(Boolean).join(' ');
  }

  getCommentClasses(): string {
    const baseClasses = 'bg-white border transition-colors';
    const variantClasses = {
      default: 'border-gray-200 hover:border-gray-300',
      primary: 'border-blue-200 hover:border-blue-300',
      secondary: 'border-purple-200 hover:border-purple-300',
      success: 'border-green-200 hover:border-green-300',
      warning: 'border-yellow-200 hover:border-yellow-300',
      danger: 'border-red-200 hover:border-red-300'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    const paddingClasses = this.compactMode ? 'p-3' : 'p-4';

    return [
      baseClasses,
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      paddingClasses
    ].filter(Boolean).join(' ');
  }

  trackByComment(index: number, comment: CommentItem): any {
    return comment.id;
  }
}
