<div
    [class]="computedClasses()"
    [attr.aria-label]="ariaLabel() || 'Dynamic list'"
    role="region"
  >
  <!-- Header Section with Search, Filters, and Actions -->
  <div class="dynamic-list-header" *ngIf="showHeader()">
    <!-- Title and Description -->
    <div class="header-content" *ngIf="title() || description()">
      <h3 class="list-title" *ngIf="title()">{{ title() }}</h3>
      <p class="list-description" *ngIf="description()">{{ description() }}</p>
    </div>

    <!-- Controls Row -->
    <div class="header-controls" *ngIf="showSearch() || showFilters() || showSorting() || headerActionsComputed().length > 0">
      <!-- Search Input -->
      <div class="search-container" *ngIf="showSearch()">
        <div class="search-input-wrapper">
          <input
            #searchInput
            type="text"
            [class]="searchInputClasses()"
            [placeholder]="searchPlaceholder()"
            [value]="searchTerm()"
            (input)="onSearchInput($event)"
            [attr.aria-label]="'Search ' + (title() || 'items')"
            autocomplete="off"
          />
          <div class="search-icon">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <button 
            *ngIf="searchTerm()"
            class="search-clear"
            (click)="clearSearch()"
            [attr.aria-label]="'Clear search'"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Filter Controls -->
      <div class="filter-container" *ngIf="showFilters() && filterConfigs().length > 0">
        <select
          *ngFor="let filter of filterConfigs(); trackBy: trackByFilterField"
          [class]="filterSelectClasses()"
          [value]="getActiveFilterValue(filter.field)"
          (change)="handleFilterChangeEvent(filter.field, $event)"
          [attr.aria-label]="'Filter by ' + filter.label"
        >
          <option value="">{{ filter.placeholder || 'All ' + filter.label }}</option>
          <option *ngFor="let option of filter.options; trackBy: trackByFilterOption" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Sort Controls -->
      <div class="sort-container" *ngIf="showSorting() && sortOptionsComputed().length > 0">
        <select
          [class]="sortSelectClasses()"
          [value]="getCurrentSortValue()"
          (change)="handleSortChangeEvent($event)"
          [attr.aria-label]="'Sort options'"
        >
          <option value="">{{ searchPlaceholder() || 'Sort by...' }}</option>
          <option *ngFor="let option of sortOptionsComputed(); trackBy: trackBySortOption" [value]="option.field + '_' + option.direction">
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- Header Actions -->
      <div class="header-actions" *ngIf="headerActionsComputed().length > 0">
        <button
          *ngFor="let action of headerActionsComputed(); trackBy: trackByAction"
          [class]="getActionClasses(action)"
          [disabled]="action.disabled"
          (click)="onActionClick(action)"
          [attr.aria-label]="action.ariaLabel || action.label"
          [title]="action.tooltip"
        >
          <span *ngIf="action.icon" class="action-icon">{{ action.icon }}</span>
          <span *ngIf="action.label" class="action-label">{{ action.label }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="loading()">
    <div class="loading-skeleton" *ngFor="let i of getSkeletonArray(); trackBy: trackByIndex">
      <div class="skeleton-item">
        <div class="skeleton-avatar" *ngIf="layout() !== 'minimal'"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-subtitle" *ngIf="layout() === 'detailed'"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-container" *ngIf="!loading() && filteredItems().length === 0">
    <div class="empty-content">
      <div class="empty-icon" *ngIf="emptyStateIcon()">
        {{ emptyStateIcon() }}
      </div>
      <h4 class="empty-title">{{ emptyStateTitle() || 'No items found' }}</h4>
      <p class="empty-message" *ngIf="emptyStateMessage()">{{ emptyStateMessage() }}</p>
      <button
        *ngIf="emptyStateAction()"
        [class]="emptyActionClasses()"
        (click)="onEmptyActionClick()"
        [attr.aria-label]="emptyStateAction()?.ariaLabel || emptyStateAction()?.label"
      >
        {{ emptyStateAction()?.label }}
      </button>
    </div>
  </div>

  <!-- List Content -->
  <div class="list-container" *ngIf="!loading() && filteredItems().length > 0">
    <!-- Virtual Scroll Container -->
    <div
      #virtualScrollContainer
      class="virtual-scroll-container"
      *ngIf="virtualScroll().enabled"
      [style.height.px]="virtualScroll().containerHeight"
      (scroll)="onVirtualScroll($event)"
    >
      <div 
        class="virtual-scroll-content"
        [style.height.px]="virtualScrollHeight()"
        [style.padding-top.px]="virtualScrollOffset()"
      >
        <div
          *ngFor="let item of visibleItems(); let i = index; trackBy: trackByItem"
          [class]="getItemClasses(item.item, i)  // Access item property of the wrapper object"
          [attr.data-item-id]="item.item.id"
          [attr.role]="getItemRole(item.item)"
          [attr.aria-selected]="item.item.selected"
          [attr.tabindex]="getItemTabIndex(item.item, i)"
          (click)="onItemClick(item.item, $event)"
          (keydown)="onItemKeydown(item.item, $event, i)"
          (mouseenter)="handleItemHover(item.item, $event) // Pass item.item here"
          (focus)="onItemFocus(item.item, $event)"
        >
          <ng-container [ngTemplateOutlet]="itemTemplate" [ngTemplateOutletContext]="{ $implicit: item.item, index: i }"></ng-container>
        </div>
      </div>
    </div>

    <!-- Regular List Container -->
    <div class="regular-list-container" *ngIf="!virtualScroll().enabled">
      <div
        *ngFor="let item of paginatedItems(); let i = index; trackBy: trackByItem"
        [class]="getItemClasses(item, i)"
        [attr.data-item-id]="item.id"
        [attr.role]="getItemRole(item)"
        [attr.aria-selected]="item.selected"
        [attr.tabindex]="getItemTabIndex(item, i)"
        (click)="onItemClick(item, $event)"
        (keydown)="onItemKeydown(item, $event, i)"
        (mouseenter)="handleItemHover(item, $event)"
        (focus)="onItemFocus(item, $event)"
      >
        <ng-container [ngTemplateOutlet]="itemTemplate" [ngTemplateOutletContext]="{ $implicit: item, index: i }"></ng-container>
      </div>
    </div>
  </div>

  <!-- Pagination -->
  <div class="pagination-container" *ngIf="showPagination() && !virtualScroll().enabled && totalPages() > 1">
    <div class="pagination-info">
      <span class="pagination-text">
        Showing {{ getDisplayStart() }}-{{ getDisplayEnd() }} of {{ totalItems() }} items
      </span>
    </div>
    <div class="pagination-controls">
      <button
        class="pagination-button pagination-prev"
        [disabled]="currentPage() === 1"
        (click)="goToPage(currentPage() - 1)"
        [attr.aria-label]="'Previous page'"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      
      <div class="pagination-numbers">
        <button
          *ngFor="let page of getVisiblePages(); trackBy: trackByIndex"
          [class]="getPaginationButtonClasses(page)"
          [disabled]="page === '...'"
          (click)="page !== '...' && goToPage(+page)"
          [attr.aria-label]="page === '...' ? null : 'Go to page ' + page"
        >
          {{ page }}
        </button>
      </div>

      <button
        class="pagination-button pagination-next"
        [disabled]="currentPage() === totalPages()"
        (click)="goToPage(currentPage() + 1)"
        [attr.aria-label]="'Next page'"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    </div>
  </div>

  <!-- Item Template -->
  <ng-template #itemTemplate let-item let-index="index">
    <!-- Multi-select Checkbox -->
    <div class="item-checkbox" *ngIf="multiSelect()">
      <input
        type="checkbox"
        [checked]="item.selected"
        (change)="onItemSelect(item, $event)"
        [attr.aria-label]="'Select ' + item.title"
        (click)="$event.stopPropagation()"
      />
    </div>

    <!-- Drag Handle -->
    <div class="item-drag-handle" *ngIf="allowReorder()">
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
      </svg>
    </div>

    <!-- Item Content -->
    <div class="item-content">
      <!-- Avatar/Icon -->
      <div class="item-visual" *ngIf="layout() !== 'minimal' && (item.avatar || item.icon)">
        <img 
          *ngIf="item.avatar" 
          [src]="item.avatar" 
          [alt]="item.title"
          class="item-avatar"
          (error)="onImageError($event, item)"
        />
        <div *ngIf="!item.avatar && item.icon" class="item-icon">
          {{ item.icon }}
        </div>
      </div>

      <!-- Text Content -->
      <div class="item-text">
        <div class="item-title-row">
          <h4 class="item-title">{{ item.title }}</h4>
          <div class="item-badges" *ngIf="item.badge">
            <span [class]="getBadgeClasses(item.badge)">
              {{ item.badge.text }}
            </span>
          </div>
        </div>
        
        <p class="item-subtitle" *ngIf="item.subtitle && layout() !== 'minimal'">
          {{ item.subtitle }}
        </p>
        
        <p class="item-description" *ngIf="item.description && layout() === 'detailed'">
          {{ item.description }}
        </p>

        <!-- Tags -->
        <div class="item-tags" *ngIf="item.tags && item.tags.length > 0 && layout() === 'detailed'">
          <span *ngFor="let tag of item.tags; trackBy: trackByTag" class="item-tag">
            {{ tag }}
          </span>
        </div>

        <!-- Metadata -->
        <div class="item-metadata" *ngIf="item.date || item.status || item.priority">
          <span class="item-date" *ngIf="item.date">
            {{ formatDate(item.date) }}
          </span>
          <span class="item-status" *ngIf="item.status" [attr.data-status]="item.status">
            {{ getStatusLabel(item.status) }}
          </span>
          <span class="item-priority" *ngIf="item.priority" [attr.data-priority]="item.priority">
            {{ getPriorityLabel(item.priority) }}
          </span>
        </div>
      </div>

      <!-- Actions -->
      <div class="item-actions" *ngIf="itemActionsComputed().length > 0">
        <button
          *ngFor="let action of itemActionsComputed(); trackBy: trackByAction"
          [class]="getItemActionClasses(action)"
          [disabled]="action.disabled"
          (click)="onItemActionClick(action, item, $event)"
          [attr.aria-label]="action.ariaLabel || action.label"
          [title]="action.tooltip"
        >
          <span *ngIf="action.icon" class="action-icon">{{ action.icon }}</span>
          <span *ngIf="action.label && layout() === 'detailed'" class="action-label">{{ action.label }}</span>
          <span *ngIf="action.label && layout() !== 'detailed' && !action.icon" class="action-label">{{ action.label }}</span>
        </button>
      </div>
    </div>
  </ng-template>
</div>
