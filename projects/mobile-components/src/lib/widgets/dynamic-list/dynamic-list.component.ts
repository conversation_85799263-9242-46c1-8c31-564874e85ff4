import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  OnInit,
  OnDestroy,
  computed,
  signal,
  effect,
  ChangeDetectionStrategy,
  WritableSignal,
  Signal
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, debounceTime, distinctUntilChanged, takeUntil } from 'rxjs';

// Interface for list item data structure
export interface ListItem {
  id: string | number;
  title: string;
  subtitle?: string;
  description?: string;
  avatar?: string;
  icon?: string;
  badge?: {
    text?: string;
    color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    variant?: 'solid' | 'outline' | 'soft';
  };
  metadata?: Record<string, any>;
  tags?: string[];
  status?: 'active' | 'inactive' | 'pending' | 'disabled';
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  date?: Date | string;
  href?: string;
  target?: '_blank' | '_self' | '_parent' | '_top';
  disabled?: boolean;
  selected?: boolean;
}

// Interface for sort configuration
export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
  label?: string;
}

// Interface for filter configuration
export interface FilterConfig {
  field: string;
  value: any;
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte';
  label?: string;
  placeholder?: string;
  options?: { value: any; label: string }[];
}

// Interface for available filter options
export interface FilterConfigOption {
  field: string;
  label?: string;
  placeholder?: string;
  options: { value: any; label: string }[];
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'gte' | 'lte';
}

// Interface for pagination configuration
export interface DynamicListPaginationConfig {
  currentPage: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  showPageNumbers?: boolean;
  maxPageButtons?: number;
}

// Interface for virtual scrolling configuration
export interface VirtualScrollConfig {
  enabled: boolean;
  itemHeight: number;
  containerHeight?: number;
  bufferSize?: number;
  threshold?: number;
}

@Component({
  selector: 'base-dynamic-list',
  templateUrl: './dynamic-list.component.html',
  styleUrls: ['./dynamic-list.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  // Force re-evaluation
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DynamicListComponent implements OnInit, OnDestroy {
  @ViewChild('listContainer', { static: false }) listContainer!: ElementRef<HTMLElement>;
  @ViewChild('virtualScrollContainer', { static: false }) virtualScrollContainer!: ElementRef<HTMLElement>;
  
  // Standard Tailwind inputs
  className: WritableSignal<string> = signal<string>('');
  size: WritableSignal<'xs' | 'sm' | 'md' | 'lg' | 'xl'> = signal<'xs' | 'sm' | 'md' | 'lg' | 'xl'>('md');
  variant: WritableSignal<'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'> = signal<'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger'>('default');
  rounded: WritableSignal<'none' | 'sm' | 'md' | 'lg' | 'full'> = signal<'none' | 'sm' | 'md' | 'lg' | 'full'>('md');

  // Header and content inputs
  title: WritableSignal<string> = signal<string>('');
  description: WritableSignal<string> = signal<string>('');
  ariaLabel: WritableSignal<string> = signal<string>('');

  // Component-specific inputs with default data
  items: WritableSignal<ListItem[]> = signal<ListItem[]>([
    {
      id: '1',
      title: 'Sample Task 1',
      subtitle: 'Important Project',
      description: 'This is a sample task description that demonstrates the dynamic list functionality.',
      avatar: '',
      icon: 'task',
      badge: { text: 'High', color: 'danger', variant: 'solid' },
      status: 'active',
      priority: 'high',
      date: new Date(),
      tags: ['urgent', 'frontend'],
      selected: false
    },
    {
      id: '2',
      title: 'Sample Task 2',
      subtitle: 'Development',
      description: 'Another task demonstrating sorting and filtering capabilities.',
      avatar: '',
      icon: 'development',
      badge: { text: 'Medium', color: 'warning', variant: 'solid' },
      status: 'pending',
      priority: 'medium',
      date: new Date(Date.now() - 86400000),
      tags: ['backend', 'api'],
      selected: false
    },
    {
      id: '3',
      title: 'Sample Task 3',
      subtitle: 'Testing',
      description: 'A third item to show multiple items in the list view.',
      avatar: '',
      icon: 'testing',
      badge: { text: 'Low', color: 'success', variant: 'soft' },
      status: 'active',
      priority: 'low',
      date: new Date(Date.now() - 172800000),
      tags: ['testing', 'qa'],
      selected: false
    }
  ]);

  // Display and behavior options
  layout: WritableSignal<'list' | 'grid' | 'cards' | 'minimal' | 'detailed'> = signal<'list' | 'grid' | 'cards' | 'minimal' | 'detailed'>('list');
  density: WritableSignal<'compact' | 'comfortable' | 'spacious'> = signal<'compact' | 'comfortable' | 'spacious'>('comfortable');
  showSearch: WritableSignal<boolean> = signal<boolean>(true);
  showFilters: WritableSignal<boolean> = signal<boolean>(true);
  showSort: WritableSignal<boolean> = signal<boolean>(true);
  showSorting: WritableSignal<boolean> = signal<boolean>(true); // Alias for showSort
  showPagination: WritableSignal<boolean> = signal<boolean>(true);
  multiSelect: WritableSignal<boolean> = signal<boolean>(false);
  singleSelect: WritableSignal<boolean> = signal<boolean>(false);
  allowReorder: WritableSignal<boolean> = signal<boolean>(false);
  clickable: WritableSignal<boolean> = signal<boolean>(true);
  hoverable: WritableSignal<boolean> = signal<boolean>(true);

  // Virtual scrolling
  virtualScroll: WritableSignal<VirtualScrollConfig> = signal<VirtualScrollConfig>({
    enabled: false,
    itemHeight: 80,
    containerHeight: 400,
    bufferSize: 10,
    threshold: 5
  });

  // Pagination
  pagination: WritableSignal<DynamicListPaginationConfig> = signal<DynamicListPaginationConfig>({
    currentPage: 1,
    itemsPerPage: 10,
    totalItems: 0,
    totalPages: 0,
    showFirstLast: true,
    showPrevNext: true,
    showPageNumbers: true,
    maxPageButtons: 5
  });

  // Search and filtering
  searchPlaceholder: WritableSignal<string> = signal<string>('Search items...');
  searchFields: WritableSignal<string[]> = signal<string[]>(['title', 'subtitle', 'description']);
  sortOptions: WritableSignal<SortConfig[]> = signal<SortConfig[]>([
    { field: 'title', direction: 'asc', label: 'Title (A-Z)' },
    { field: 'title', direction: 'desc', label: 'Title (Z-A)' },
    { field: 'date', direction: 'desc', label: 'Date (Newest)' },
    { field: 'date', direction: 'asc', label: 'Date (Oldest)' },
    { field: 'priority', direction: 'desc', label: 'Priority (High-Low)' }
  ]);
  filters: WritableSignal<FilterConfig[]> = signal<FilterConfig[]>([]);
  availableFilters: WritableSignal<FilterConfigOption[]> = signal<FilterConfigOption[]>([]);

  // Loading and empty states
  loading: WritableSignal<boolean> = signal<boolean>(false);
  loadingCount: WritableSignal<number> = signal<number>(5);
  emptyStateTitle: WritableSignal<string> = signal<string>('No items found');
  emptyStateMessage: WritableSignal<string> = signal<string>('Try adjusting your search or filters');
  emptyStateIcon: WritableSignal<string> = signal<string>('');
  emptyStateAction: WritableSignal<any> = signal<any>(null);

  // Actions
  headerActions: WritableSignal<any[]> = signal<any[]>([]);
  itemActions: WritableSignal<any[]> = signal<any[]>([]);

  // Event outputs
  @Output() itemClick = new EventEmitter<ListItem>();
  @Output() itemDoubleClick = new EventEmitter<ListItem>();
  @Output() itemHover = new EventEmitter<ListItem>();
  @Output() selectionChange = new EventEmitter<ListItem[]>();
  @Output() sortChange = new EventEmitter<SortConfig>();
  @Output() filterChange = new EventEmitter<FilterConfig[]>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() pageChange = new EventEmitter<number>();
  @Output() itemsPerPageChange = new EventEmitter<number>();
  @Output() dragStart = new EventEmitter<{ item: ListItem; index: number }>();
  @Output() dragEnd = new EventEmitter<{ item: ListItem; fromIndex: number; toIndex: number }>();

  // Internal state signals
  private destroy$ = new Subject<void>();
  searchQuery: WritableSignal<string> = signal<string>('');
  searchTerm: WritableSignal<string> = signal<string>('');
  currentSort: WritableSignal<SortConfig | null> = signal<SortConfig | null>(null);
  activeFilters: WritableSignal<FilterConfig[]> = signal<FilterConfig[]>([]);
  selectedItems: WritableSignal<Set<string | number>> = signal<Set<string | number>>(new Set());
  currentPage: WritableSignal<number> = signal<number>(1);
  scrollTop: WritableSignal<number> = signal<number>(0);
  focusedIndex: WritableSignal<number> = signal<number>(-1);

  // Computed properties
  computedClasses: Signal<string> = computed(() => {
    const classes = [
      'dynamic-list-container',
      `size-${this.size()}`,
      `variant-${this.variant()}`,
      `layout-${this.layout()}`,
      `density-${this.density()}`,
      `rounded-${this.rounded()}`
    ];

    if (this.loading()) classes.push('loading');
    if (this.className()) classes.push(this.className());

    return classes.join(' ');
  });

  // Computed property for selected items as array
  selectedItemsArray: Signal<ListItem[]> = computed(() => {
    const selectedIds = this.selectedItems();
    return this.filteredItems().filter(item => selectedIds.has(item.id));
  });

  filteredItems: Signal<ListItem[]> = computed(() => {
    let result = [...this.items()];
    
    // Apply search
    const query = this.searchQuery().toLowerCase().trim();
    if (query) {
      result = result.filter(item => 
        this.searchFields().some(field => {
          const value = this.getNestedValue(item, field);
          return value && value.toString().toLowerCase().includes(query);
        })
      );
    }

    // Apply filters
    const filters = this.activeFilters();
    filters.forEach(filter => {
      result = result.filter(item => this.applyFilter(item, filter));
    });

    // Apply sort
    const sort = this.currentSort();
    if (sort) {
      result.sort((a, b) => this.compareItems(a, b, sort));
    }

    return result;
  });

  paginatedItems: Signal<ListItem[]> = computed(() => {
    const filtered = this.filteredItems();
    
    if (!this.showPagination()) {
      return filtered;
    }

    const currentPagination = this.pagination();
    const startIndex = (currentPagination.currentPage - 1) * currentPagination.itemsPerPage;
    const endIndex = startIndex + currentPagination.itemsPerPage;
    
    return filtered.slice(startIndex, endIndex);
  });

  visibleItems: Signal<{ item: ListItem; index: number }[]> = computed(() => {
    if (!this.virtualScroll().enabled) {
      return this.paginatedItems().map((item, index) => ({ item, index }));
    }

    return this.getVirtualItems();
  });

  totalFilteredItems: Signal<number> = computed(() => this.filteredItems().length);

  totalItems: Signal<number> = computed(() => this.totalFilteredItems());
  
  totalPages: Signal<number> = computed(() => {
    if (!this.showPagination()) return 1;
    const currentPagination = this.pagination();
    return Math.ceil(this.totalFilteredItems() / currentPagination.itemsPerPage);
  });

  // Computed properties for template access
  filterConfigs: Signal<FilterConfigOption[]> = computed(() => {
    return this.availableFilters();
  });

  sortOptionsComputed: Signal<SortConfig[]> = computed(() => {
    return this.sortOptions();
  });

  headerActionsComputed: Signal<any[]> = computed(() => {
    return this.headerActions();
  });

  itemActionsComputed: Signal<any[]> = computed(() => {
    return this.itemActions();
  });

  // Virtual scrolling computed properties
  virtualScrollHeight: Signal<number> = computed(() => {
    const totalItems = this.paginatedItems().length;
    const itemHeight = this.virtualScroll().itemHeight;
    return totalItems * itemHeight;
  });

  virtualScrollOffset: Signal<number> = computed(() => {
    const scrollTop = this.scrollTop();
    const itemHeight = this.virtualScroll().itemHeight;
    const bufferSize = this.virtualScroll().bufferSize || 10;
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
    return startIndex * itemHeight;
  });

  ngOnInit(): void {
    // Update pagination total items when filtered items change
    effect(() => {
      const currentPagination = this.pagination();
      const totalItems = this.totalFilteredItems();
      const totalPages = Math.ceil(totalItems / currentPagination.itemsPerPage);
      
      this.pagination.set({
        ...currentPagination,
        totalItems,
        totalPages
      });
    });

    // Setup virtual scrolling if enabled
    if (this.virtualScroll().enabled) {
      this.setupVirtualScrolling();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // Method to be called from the template for item hover
  public handleItemHover(item: ListItem, event: Event): void {
    this.itemHover.emit(item);
    // Add any other hover logic if needed
  }

  // Method to be called from the template for sort change
  public handleSortChangeEvent(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const value = selectElement.value;
    if (value) {
      const [field, direction] = value.split('_');
      const newSortConfig: SortConfig = { field, direction: direction as 'asc' | 'desc' };
      this.currentSort.set(newSortConfig);
      this.sortChange.emit(newSortConfig);
    } else {
      this.currentSort.set(null);
      // Optionally emit a default or null sort config
      // this.sortChange.emit(undefined); 
    }
  }

  // Method to be called from the template for filter change
  public handleFilterChangeEvent(field: string, event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const value = selectElement.value;
    
    let updatedFilters = [...this.activeFilters()];
    const existingFilterIndex = updatedFilters.findIndex(f => f.field === field);

    if (value === '') { // Value cleared or "All" selected
      if (existingFilterIndex > -1) {
        updatedFilters.splice(existingFilterIndex, 1);
      }
    } else {
      const newFilter: FilterConfig = { field, value }; // Operator can be added if needed
      if (existingFilterIndex > -1) {
        updatedFilters[existingFilterIndex] = newFilter;
      } else {
        updatedFilters.push(newFilter);
      }
    }
    this.activeFilters.set(updatedFilters);
    this.filterChange.emit(updatedFilters);
  }

  // Methods that template expects to call as functions
  showHeader(): boolean {
    return !!(this.title() || this.description() || this.showSearch() || this.showFilters() || this.showSort() || this.headerActions().length > 0);
  }

  sortPlaceholder(): string {
    return 'Sort by...';
  }

  // Template helper methods
  clearSearch(): void {
    this.searchTerm.set('');
    this.searchQuery.set('');
  }

  getActiveFilterValue(field: string): any {
    const filter = this.activeFilters().find(f => f.field === field);
    return filter ? filter.value : '';
  }

  getCurrentSortValue(): string {
    const sort = this.currentSort();
    return sort ? `${sort.field}_${sort.direction}` : '';
  }

  getSkeletonArray(): number[] {
    return Array.from({ length: this.loadingCount() }, (_, i) => i);
  }

  // CSS class computation methods
  searchInputClasses(): string {
    const baseClasses = ['search-input', 'form-input', `size-${this.size()}`];
    return baseClasses.join(' ');
  }

  filterSelectClasses(): string {
    const baseClasses = ['filter-select', 'form-select', `size-${this.size()}`];
    return baseClasses.join(' ');
  }

  sortSelectClasses(): string {
    const baseClasses = ['sort-select', 'form-select', `size-${this.size()}`];
    return baseClasses.join(' ');
  }

  emptyActionClasses(): string {
    const baseClasses = ['empty-action-btn', 'btn', `btn-${this.variant()}`, `size-${this.size()}`];
    return baseClasses.join(' ');
  }

  getItemClasses(item: ListItem, index: number): string {
    const baseClasses = [
      'list-item',
      `layout-${this.layout()}`,
      `density-${this.density()}`,
      `size-${this.size()}`
    ];

    if (item.selected || this.selectedItems().has(item.id)) {
      baseClasses.push('selected');
    }

    if (item.disabled) {
      baseClasses.push('disabled');
    }

    if (this.clickable() && !item.disabled) {
      baseClasses.push('clickable');
    }

    if (this.hoverable()) {
      baseClasses.push('hoverable');
    }

    return baseClasses.join(' ');
  }

  getBadgeClasses(badge: any): string {
    const baseClasses = ['badge', `badge-${badge.variant || 'solid'}`, `badge-${badge.color || 'default'}`];
    return baseClasses.join(' ');
  }

  getActionClasses(action: any): string {
    return 'action-btn btn btn-sm btn-outline';
  }

  getItemActionClasses(action: any): string {
    return 'item-action-btn btn btn-xs btn-ghost';
  }

  getPaginationButtonClasses(page: number | string): string {
    const baseClasses = ['pagination-btn', 'btn', 'btn-sm'];
    
    const currentPagination = this.pagination();
    if (page === currentPagination.currentPage) {
      baseClasses.push('btn-primary', 'active');
    } else if (page === '...') {
      baseClasses.push('btn-ghost', 'cursor-default');
    } else {
      baseClasses.push('btn-ghost');
    }

    return baseClasses.join(' ');
  }

  // Event handlers
  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    const query = target.value;
    this.searchTerm.set(query);
    
    // Debounce search input
    const searchSubject = new Subject<string>();
    searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged(),
      takeUntil(this.destroy$)
    ).subscribe(searchQuery => {
      this.searchQuery.set(searchQuery);
      this.searchChange.emit(searchQuery);
      this.resetPagination();
    });
    
    searchSubject.next(query);
  }

  onFilterChange(field: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    const value = target.value;
    const filter = this.filters().find(f => f.field === field);
    
    if (filter) {
      if (value) {
        this.addFilter({ ...filter, value });
      } else {
        this.removeFilter(field);
      }
    }
  }

  onSortChange(event: Event): void {
    const target = event.target as HTMLSelectElement;
    const value = target.value;
    
    if (value) {
      const [field, direction] = value.split('_');
      this.onSortChangeEvent({ field, direction: direction as 'asc' | 'desc' });
    } else {
      this.currentSort.set(null);
    }
  }

  onSortChangeEvent(sort: SortConfig): void {
    this.currentSort.set(sort);
    this.sortChange.emit(sort);
    this.resetPagination();
  }

  onItemClick(item: ListItem, event: Event): void {
    if (this.clickable() && !item.disabled) {
      this.itemClick.emit(item);
      this.toggleSelection(item, event as MouseEvent);
    }
  }

  onItemKeydown(item: ListItem, event: KeyboardEvent, index: number): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onItemClick(item, event);
    }
  }

  onItemFocus(item: ListItem, event: FocusEvent): void {
    // Handle focus events if needed
  }

  onItemHover(item: ListItem, event: Event): void {
    this.itemHover.emit(item);
  }

  onItemSelect(item: ListItem, event: Event): void {
    const target = event.target as HTMLInputElement;
    const selectedIds = new Set(this.selectedItems());
    
    if (target.checked) {
      selectedIds.add(item.id);
    } else {
      selectedIds.delete(item.id);
    }
    
    this.selectedItems.set(selectedIds);
    this.selectionChange.emit(this.selectedItemsArray());
  }

  onImageError(event: Event, item: ListItem): void {
    const img = event.target as HTMLImageElement;
    img.style.display = 'none';
  }

  onItemActionClick(action: any, item: ListItem, event: Event): void {
    event.stopPropagation();
    if (action.handler) {
      action.handler(item);
    }
  }

  onActionClick(action: any): void {
    if (action.handler) {
      action.handler();
    }
  }

  onEmptyActionClick(): void {
    const emptyAction = this.emptyStateAction();
    if (emptyAction && emptyAction.handler) {
      emptyAction.handler();
    }
  }

  onVirtualScroll(event: Event): void {
    const target = event.target as HTMLElement;
    this.scrollTop.set(target.scrollTop);
  }

  // Utility methods
  getItemRole(item: ListItem): string {
    return this.clickable() ? 'button' : 'listitem';
  }

  getItemTabIndex(item: ListItem, index: number): number {
    return this.clickable() ? 0 : -1;
  }

  formatDate(date: Date | string): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString();
  }

  getStatusLabel(status: string): string {
    const statusLabels: Record<string, string> = {
      'active': 'Active',
      'inactive': 'Inactive',
      'pending': 'Pending',
      'disabled': 'Disabled'
    };
    return statusLabels[status] || status;
  }

  getPriorityLabel(priority: string): string {
    const priorityLabels: Record<string, string> = {
      'low': 'Low',
      'medium': 'Medium',
      'high': 'High',
      'urgent': 'Urgent'
    };
    return priorityLabels[priority] || priority;
  }

  // Pagination methods
  goToPage(page: number | string): void {
    if (typeof page === 'number' && page >= 1 && page <= this.totalPages()) {
      this.currentPage.set(page);
      const currentPagination = this.pagination();
      this.pagination.set({
        ...currentPagination,
        currentPage: page
      });
      this.pageChange.emit(page);
    }
  }

  getDisplayStart(): number {
    if (this.totalFilteredItems() === 0) return 0;
    const currentPagination = this.pagination();
    return (currentPagination.currentPage - 1) * currentPagination.itemsPerPage + 1;
  }

  getDisplayEnd(): number {
    const currentPagination = this.pagination();
    const end = currentPagination.currentPage * currentPagination.itemsPerPage;
    return Math.min(end, this.totalFilteredItems());
  }

  getVisiblePages(): (number | string)[] {
    const currentPagination = this.pagination();
    const current = currentPagination.currentPage;
    const total = this.totalPages();
    const maxVisible = currentPagination.maxPageButtons || 5;
    
    if (total <= maxVisible) {
      return Array.from({ length: total }, (_, i) => i + 1);
    }
    
    const pages: (number | string)[] = [];
    const half = Math.floor(maxVisible / 2);
    
    let start = Math.max(1, current - half);
    let end = Math.min(total, start + maxVisible - 1);
    
    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }
    
    if (start > 1) {
      pages.push(1);
      if (start > 2) pages.push('...');
    }
    
    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    
    if (end < total) {
      if (end < total - 1) pages.push('...');
      pages.push(total);
    }
    
    return pages;
  }

  // Track by functions for performance
  trackByItemId(index: number, item: any): string | number {
    return item.item ? item.item.id : item.id;
  }

  trackByFilterField(index: number, filter: FilterConfigOption): string {
    return filter.field;
  }

  trackBySortOption(index: number, option: SortConfig): string {
    return `${option.field}_${option.direction}`;
  }

  trackByAction(index: number, action: any): string {
    return action.id || action.label || index.toString();
  }

  trackByTag(index: number, tag: string): string {
    return tag;
  }

  trackByFilterOption(index: number, option: any): string {
    return option.value;
  }

  trackByIndex(index: number): number {
    return index;
  }

  trackByItem(index: number, item: any): string | number {
    return item.item ? item.item.id : item.id;
  }

  // Private helper methods
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private applyFilter(item: ListItem, filter: FilterConfig): boolean {
    const value = this.getNestedValue(item, filter.field);
    if (value == null) return false;

    switch (filter.operator || 'contains') {
      case 'equals':
        return value === filter.value;
      case 'contains':
        return value.toString().toLowerCase().includes(filter.value.toString().toLowerCase());
      case 'startsWith':
        return value.toString().toLowerCase().startsWith(filter.value.toString().toLowerCase());
      case 'endsWith':
        return value.toString().toLowerCase().endsWith(filter.value.toString().toLowerCase());
      case 'gt':
        return Number(value) > Number(filter.value);
      case 'lt':
        return Number(value) < Number(filter.value);
      case 'gte':
        return Number(value) >= Number(filter.value);
      case 'lte':
        return Number(value) <= Number(filter.value);
      default:
        return true;
    }
  }

  private compareItems(a: ListItem, b: ListItem, sort: SortConfig): number {
    const aValue = this.getNestedValue(a, sort.field);
    const bValue = this.getNestedValue(b, sort.field);

    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return sort.direction === 'asc' ? -1 : 1;
    if (bValue == null) return sort.direction === 'asc' ? 1 : -1;

    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const result = aValue.localeCompare(bValue);
      return sort.direction === 'asc' ? result : -result;
    }

    if (aValue instanceof Date && bValue instanceof Date) {
      const result = aValue.getTime() - bValue.getTime();
      return sort.direction === 'asc' ? result : -result;
    }

    // Handle priority special case
    if (sort.field === 'priority') {
      const priorityOrder = { 'low': 1, 'medium': 2, 'high': 3, 'urgent': 4 };
      const aPriority = priorityOrder[aValue as keyof typeof priorityOrder] || 0;
      const bPriority = priorityOrder[bValue as keyof typeof priorityOrder] || 0;
      const result = aPriority - bPriority;
      return sort.direction === 'asc' ? result : -result;
    }

    // Default numeric/string comparison
    if (aValue < bValue) return sort.direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return sort.direction === 'asc' ? 1 : -1;
    return 0;
  }

  private resetPagination(): void {
    this.currentPage.set(1);
    const currentPagination = this.pagination();
    this.pagination.set({
      ...currentPagination,
      currentPage: 1
    });
  }

  private setupVirtualScrolling(): void {
    // Setup virtual scrolling logic
  }

  private getVirtualItems(): any[] {
    const scrollTop = this.scrollTop();
    const itemHeight = this.virtualScroll().itemHeight;
    const bufferSize = this.virtualScroll().bufferSize || 10;
    const containerHeight = 400; // Default height
    
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferSize);
    const endIndex = Math.min(
      this.paginatedItems().length,
      startIndex + Math.ceil(containerHeight / itemHeight) + bufferSize * 2
    );
    
    return this.paginatedItems()
      .slice(startIndex, endIndex)
      .map((item, index) => ({ item, index: startIndex + index }));
  }

  private toggleSelection(item: ListItem, event?: MouseEvent): void {
    const selectedIds = new Set(this.selectedItems());
    
    if (this.singleSelect()) {
      selectedIds.clear();
      selectedIds.add(item.id);
    } else if (this.multiSelect()) {
      if (event?.ctrlKey || event?.metaKey) {
        // Toggle individual item
        if (selectedIds.has(item.id)) {
          selectedIds.delete(item.id);
        } else {
          selectedIds.add(item.id);
        }
      } else {
        // Replace selection
        selectedIds.clear();
        selectedIds.add(item.id);
      }
    }
    
    this.selectedItems.set(selectedIds);
    this.selectionChange.emit(this.selectedItemsArray());
  }

  private addFilter(filter: FilterConfig): void {
    const currentFilters = this.activeFilters();
    const existingIndex = currentFilters.findIndex(f => f.field === filter.field);
    
    if (existingIndex >= 0) {
      currentFilters[existingIndex] = filter;
    } else {
      currentFilters.push(filter);
    }
    
    this.activeFilters.set([...currentFilters]);
    this.filterChange.emit(this.activeFilters());
    this.resetPagination();
  }

  private removeFilter(field: string): void {
    const currentFilters = this.activeFilters().filter(f => f.field !== field);
    this.activeFilters.set(currentFilters);
    this.filterChange.emit(this.activeFilters());
    this.resetPagination();
  }

  private clearAllFilters(): void {
    this.activeFilters.set([]);
    this.filterChange.emit([]);
    this.resetPagination();
  }

  // Public selection methods
  selectAll(): void {
    const allIds = this.filteredItems().map(item => item.id);
    this.selectedItems.set(new Set(allIds));
    this.selectionChange.emit(this.selectedItemsArray());
  }

  clearSelection(): void {
    this.selectedItems.set(new Set());
    this.selectionChange.emit([]);
  }
}