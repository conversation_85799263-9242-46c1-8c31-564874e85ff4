<div [class]="containerClasses">
  <!-- Search Bar -->
  <div *ngIf="allowSearch" class="mb-4">
    <div class="relative">
      <input
        type="text"
        placeholder="Search followers..."
        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        [(ngModel)]="searchTerm"
        (input)="onSearchChange(searchTerm)"
      />
      <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
    </div>
  </div>

  <!-- Followers List -->
  <div
    *ngFor="let follower of displayedFollowers; trackBy: trackByFollower"
    [class]="getFollowerCardClasses()"
    (click)="onFollowerClick(follower)"
    [attr.aria-label]="'Follower: ' + follower.name"
    role="button"
    tabindex="0"
  >
    <div class="flex items-start space-x-3">
      <!-- Avatar -->
      <div class="flex-shrink-0 relative">
        <div
          *ngIf="!follower.avatar"
          class="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center text-sm font-medium text-gray-700"
        >
          {{ getAvatarInitials(follower.name) }}
        </div>
        <img
          *ngIf="follower.avatar"
          [src]="follower.avatar"
          [alt]="follower.name"
          class="w-12 h-12 rounded-full object-cover"
        />

        <!-- Online Status -->
        <div
          *ngIf="showOnlineStatus"
          class="absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white"
          [class.bg-green-400]="follower.isOnline"
          [class.bg-gray-400]="!follower.isOnline"
          [attr.title]="follower.isOnline ? 'Online' : (follower.lastSeen ? 'Last seen ' + formatLastSeen(follower.lastSeen) : 'Offline')"
        ></div>
      </div>

      <!-- User Info -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center space-x-2 mb-1">
          <h3 class="text-sm font-medium text-gray-900 truncate">{{ follower.name }}</h3>

          <!-- Verified Badge -->
          <svg *ngIf="follower.isVerified" class="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </div>

        <!-- Username -->
        <p *ngIf="follower.username" class="text-sm text-gray-500 mb-1">{{ follower.username }}</p>

        <!-- Bio -->
        <p *ngIf="showBio && follower.bio" class="text-sm text-gray-600 mb-2 line-clamp-2">{{ follower.bio }}</p>

        <!-- Stats -->
        <div *ngIf="showStats" class="flex items-center space-x-4 text-xs text-gray-500 mb-2">
          <span *ngIf="follower.followersCount !== undefined">
            <strong>{{ formatFollowerCount(follower.followersCount) }}</strong> followers
          </span>
          <span *ngIf="follower.followingCount !== undefined">
            <strong>{{ formatFollowerCount(follower.followingCount) }}</strong> following
          </span>
        </div>

        <!-- Mutual Followers -->
        <p *ngIf="showMutualFollowers && follower.mutualFollowers && follower.mutualFollowers > 0"
           class="text-xs text-gray-500 mb-2">
          {{ follower.mutualFollowers }} mutual followers
        </p>

        <!-- Tags -->
        <div *ngIf="showTags && follower.tags && follower.tags.length > 0" class="flex flex-wrap gap-1 mb-2">
          <span
            *ngFor="let tag of follower.tags"
            class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
          >
            {{ tag }}
          </span>
        </div>
      </div>

      <!-- Follow Button -->
      <div *ngIf="showFollowButton" class="flex-shrink-0">
        <button
          type="button"
          class="px-4 py-2 text-sm font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2"
          [class.bg-blue-600]="!follower.isFollowing"
          [class.text-white]="!follower.isFollowing"
          [class.hover:bg-blue-700]="!follower.isFollowing"
          [class.focus:ring-blue-500]="!follower.isFollowing"
          [class.bg-gray-200]="follower.isFollowing"
          [class.text-gray-800]="follower.isFollowing"
          [class.hover:bg-gray-300]="follower.isFollowing"
          [class.focus:ring-gray-500]="follower.isFollowing"
          (click)="onFollowToggle(follower); $event.stopPropagation()"
          [attr.aria-label]="(follower.isFollowing ? 'Unfollow' : 'Follow') + ' ' + follower.name"
        >
          {{ follower.isFollowing ? 'Following' : 'Follow' }}
        </button>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="displayedFollowers.length === 0" class="text-center py-8">
    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
    </svg>
    <h3 class="text-sm font-medium text-gray-900 mb-1">No followers found</h3>
    <p class="text-sm text-gray-500">
      {{ searchTerm ? 'Try adjusting your search terms' : 'No followers to display' }}
    </p>
  </div>

  <!-- Load More -->
  <div *ngIf="maxDisplay > 0 && followers.length > maxDisplay" class="text-center pt-4">
    <p class="text-sm text-gray-500">
      Showing {{ maxDisplay }} of {{ followers.length }} followers
    </p>
  </div>
</div>
