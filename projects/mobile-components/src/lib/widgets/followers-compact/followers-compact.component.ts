import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface FollowerItem {
  id: string | number;
  name: string;
  username?: string;
  avatar?: string;
  bio?: string;
  followersCount?: number;
  followingCount?: number;
  isFollowing?: boolean;
  isVerified?: boolean;
  isOnline?: boolean;
  lastSeen?: Date;
  mutualFollowers?: number;
  tags?: string[];
}

@Component({
  selector: 'lib-followers-compact',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './followers-compact.component.html',
  styleUrl: './followers-compact.component.css'
})
export class FollowersCompactComponent {
  // Standard Tailwind customization inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() followers: FollowerItem[] = [
    {
      id: 1,
      name: '<PERSON>',
      username: '@sarah_j',
      avatar: '',
      bio: 'UX Designer passionate about creating beautiful interfaces',
      followersCount: 1250,
      followingCount: 890,
      isFollowing: false,
      isVerified: true,
      isOnline: true,
      mutualFollowers: 12,
      tags: ['Design', 'UX']
    },
    {
      id: 2,
      name: 'Mike Chen',
      username: '@mike_dev',
      avatar: '',
      bio: 'Full-stack developer | React enthusiast',
      followersCount: 2100,
      followingCount: 450,
      isFollowing: true,
      isVerified: false,
      isOnline: false,
      lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000),
      mutualFollowers: 8,
      tags: ['Development', 'React']
    },
    {
      id: 3,
      name: 'Emma Wilson',
      username: '@emma_writes',
      avatar: '',
      bio: 'Content creator and tech blogger',
      followersCount: 850,
      followingCount: 320,
      isFollowing: false,
      isVerified: true,
      isOnline: true,
      mutualFollowers: 5,
      tags: ['Writing', 'Tech']
    }
  ];

  @Input() showBio: boolean = true;
  @Input() showFollowButton: boolean = true;
  @Input() showStats: boolean = true;
  @Input() showOnlineStatus: boolean = true;
  @Input() showMutualFollowers: boolean = true;
  @Input() showTags: boolean = false;
  @Input() maxDisplay: number = 0; // 0 means show all
  @Input() layout: 'list' | 'grid' = 'list';
  @Input() allowSearch: boolean = false;

  // Event outputs
  @Output() followToggle = new EventEmitter<FollowerItem>();
  @Output() followerClick = new EventEmitter<FollowerItem>();
  @Output() searchChange = new EventEmitter<string>();

  searchTerm: string = '';

  get displayedFollowers(): FollowerItem[] {
    let filtered = this.followers;

    if (this.searchTerm) {
      filtered = filtered.filter(follower =>
        follower.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        follower.username?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        follower.bio?.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    if (this.maxDisplay > 0) {
      filtered = filtered.slice(0, this.maxDisplay);
    }

    return filtered;
  }

  onFollowToggle(follower: FollowerItem) {
    this.followToggle.emit(follower);
  }

  onFollowerClick(follower: FollowerItem) {
    this.followerClick.emit(follower);
  }

  onSearchChange(term: string) {
    this.searchTerm = term;
    this.searchChange.emit(term);
  }

  getAvatarInitials(name: string): string {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  }

  formatFollowerCount(count: number): string {
    if (count < 1000) return count.toString();
    if (count < 1000000) return (count / 1000).toFixed(1) + 'K';
    return (count / 1000000).toFixed(1) + 'M';
  }

  formatLastSeen(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  get containerClasses(): string {
    const baseClasses = 'w-full';
    const layoutClasses = this.layout === 'grid'
      ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'
      : 'space-y-3';

    return [baseClasses, layoutClasses, this.className].filter(Boolean).join(' ');
  }

  getFollowerCardClasses(): string {
    const baseClasses = 'bg-white border transition-colors hover:shadow-md';
    const variantClasses = {
      default: 'border-gray-200 hover:border-gray-300',
      primary: 'border-blue-200 hover:border-blue-300',
      secondary: 'border-purple-200 hover:border-purple-300',
      success: 'border-green-200 hover:border-green-300',
      warning: 'border-yellow-200 hover:border-yellow-300',
      danger: 'border-red-200 hover:border-red-300'
    };
    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    const sizeClasses = {
      xs: 'p-2',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-5',
      xl: 'p-6'
    };

    return [
      baseClasses,
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      sizeClasses[this.size]
    ].filter(Boolean).join(' ');
  }

  trackByFollower(index: number, follower: FollowerItem): any {
    return follower.id;
  }
}
